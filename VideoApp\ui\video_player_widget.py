# -*- coding: utf-8 -*-
"""
Enhanced Video Player Widget with editing capabilities
"""

import os
import threading
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSlider, QMessageBox, QLabel, QFrame
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QTimer
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent

# Try to import video widget with fallback
try:
    from PyQt5.QtMultimediaWidgets import QVideoWidget
    VIDEO_WIDGET_AVAILABLE = True
except ImportError:
    print("⚠️ QVideoWidget not available - using fallback")
    QVideoWidget = None
    VIDEO_WIDGET_AVAILABLE = False

# Try to import web engine with fallback
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    print("⚠️ QWebEngineView not available - using fallback")
    QWebEngineView = None
    WEB_ENGINE_AVAILABLE = False

from PyQt5.QtGui import QImage, QPixmap, QPainter, QFont, QIcon


class VideoPlayerWidget(QWidget):
    # Signals for cross-thread communication and editing features
    error_occurred = pyqtSignal(str)
    playback_started = pyqtSignal()
    frame_captured = pyqtSignal(QImage)
    time_changed = pyqtSignal(int)
    duration_loaded = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.error_message = ""
        self.is_playing = False
        self.current_frame = None
        self.markers = []
        
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

        # --- Multimedia Player (local files) ---
        if VIDEO_WIDGET_AVAILABLE:
            self.video_widget = QVideoWidget(self)
        else:
            # Fallback to a simple label
            self.video_widget = QLabel("Video playback not available\n(QVideoWidget missing)")
            self.video_widget.setAlignment(Qt.AlignCenter)
            self.video_widget.setStyleSheet("background-color: black; color: white; font-size: 14px;")

        self.player = QMediaPlayer(self)

        if VIDEO_WIDGET_AVAILABLE:
            self.player.setVideoOutput(self.video_widget)

        # Initialize audio with proper volume
        self.player.setVolume(80)  # Set default volume to 80%

        # Connect player signals
        self.player.error.connect(self.handle_player_error)
        self.player.durationChanged.connect(self._on_dur)
        self.player.positionChanged.connect(self._on_pos)
        self.player.stateChanged.connect(self._on_state_changed)

        # --- Web Player (online links) ---
        if WEB_ENGINE_AVAILABLE:
            self.webview = QWebEngineView(self)
        else:
            # Fallback to a simple label
            self.webview = QLabel("Web video playback not available\n(QWebEngineView missing)")
            self.webview.setAlignment(Qt.AlignCenter)
            self.webview.setStyleSheet("background-color: black; color: white; font-size: 14px;")

        # Player container with aspect ratio control
        player_container = QWidget(self)
        player_layout = QVBoxLayout(player_container)
        player_layout.setContentsMargins(0, 0, 0, 0)
        player_layout.addWidget(self.video_widget, 1)
        player_layout.addWidget(self.webview, 1)
        if hasattr(self.webview, 'hide'):
            self.webview.hide()

        # Time display
        time_display = QHBoxLayout()
        self.current_time_label = QLabel("00:00:00")
        self.total_time_label = QLabel("00:00:00")
        self.current_time_label.setStyleSheet("color: #ccc; font-size: 12px;")
        self.total_time_label.setStyleSheet("color: #ccc; font-size: 12px;")
        
        time_display.addWidget(self.current_time_label)
        time_display.addStretch()
        time_display.addWidget(self.total_time_label)
        
        # Timeline with markers
        timeline_container = QWidget()
        timeline_layout = QVBoxLayout(timeline_container)
        timeline_layout.setContentsMargins(0, 0, 0, 0)
        
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setRange(0, 1000)
        self.slider.sliderMoved.connect(self._on_seek)
        
        # Markers area
        self.markers_widget = QWidget()
        self.markers_widget.setFixedHeight(20)
        self.markers_widget.setStyleSheet("background: transparent;")
        
        timeline_layout.addWidget(self.slider)
        timeline_layout.addWidget(self.markers_widget)

        # Controls
        controls = QHBoxLayout()
        controls.setSpacing(5)
        
        self.btn_play = QPushButton()
        self.btn_play.setIcon(QIcon.fromTheme("media-playback-start"))
        self.btn_play.setToolTip("Play/Pause")
        self.btn_play.setFixedSize(32, 32)
        
        self.btn_stop = QPushButton()
        self.btn_stop.setIcon(QIcon.fromTheme("media-playback-stop"))
        self.btn_stop.setToolTip("Stop")
        self.btn_stop.setFixedSize(32, 32)
        
        self.btn_capture = QPushButton()
        self.btn_capture.setIcon(QIcon.fromTheme("camera-photo"))
        self.btn_capture.setToolTip("Capture Frame")
        self.btn_capture.setFixedSize(32, 32)
        
        self.btn_marker = QPushButton()
        self.btn_marker.setIcon(QIcon.fromTheme("bookmark-new"))
        self.btn_marker.setToolTip("Add Marker")
        self.btn_marker.setFixedSize(32, 32)
        
        self.btn_fullscreen = QPushButton()
        self.btn_fullscreen.setIcon(QIcon.fromTheme("view-fullscreen"))
        self.btn_fullscreen.setToolTip("Toggle Fullscreen")
        self.btn_fullscreen.setFixedSize(32, 32)

        # Volume controls
        self.volume_icon = QLabel("🔊")
        self.volume_icon.setFixedSize(24, 24)
        self.volume_icon.setAlignment(Qt.AlignCenter)
        self.volume_icon.setStyleSheet("color: #ccc; font-size: 16px;")

        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_slider.setFixedWidth(100)
        self.volume_slider.setToolTip("Volume")
        self.volume_slider.valueChanged.connect(self._on_volume_changed)

        self.volume_label = QLabel("80%")
        self.volume_label.setFixedWidth(30)
        self.volume_label.setStyleSheet("color: #ccc; font-size: 11px;")

        # Add spacing between button groups to prevent overlapping
        controls.addWidget(self.btn_play)
        controls.addSpacing(5)
        controls.addWidget(self.btn_stop)
        controls.addStretch()
        controls.addWidget(self.btn_capture)
        controls.addSpacing(5)
        controls.addWidget(self.btn_marker)
        controls.addStretch()
        controls.addWidget(self.volume_icon)
        controls.addSpacing(3)
        controls.addWidget(self.volume_slider)
        controls.addSpacing(3)
        controls.addWidget(self.volume_label)
        controls.addSpacing(5)
        controls.addWidget(self.btn_fullscreen)

        # Edit Part button
        self.btn_edit_part = QPushButton("✂️ Edit Part")
        self.btn_edit_part.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: black;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:pressed {
                background-color: #d39e00;
            }
        """)
        self.btn_edit_part.setToolTip("Edit selected video segment")
        controls.addWidget(self.btn_edit_part)

        # Add all to main layout
        self.layout.addWidget(player_container, 1)
        self.layout.addLayout(time_display)
        self.layout.addWidget(timeline_container)
        self.layout.addLayout(controls)

        # Connections
        self.btn_play.clicked.connect(self.toggle_play)
        self.btn_stop.clicked.connect(self.stop)
        self.btn_capture.clicked.connect(self.capture_current_frame)
        self.btn_marker.clicked.connect(self.add_marker)
        self.btn_fullscreen.clicked.connect(self.toggle_fullscreen)
        self.btn_edit_part.clicked.connect(self.open_video_editor)
        
        # Connect signals
        self.error_occurred.connect(self.on_error)
        self.playback_started.connect(self.on_playback_start)
        
        # Set styles
        self.set_control_styles()

    def set_control_styles(self):
        style = """
            QPushButton {
                background: #444;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background: #555;
            }
            QPushButton:pressed {
                background: #666;
            }
            QSlider::groove:horizontal {
                border: 1px solid #444;
                height: 6px;
                background: #333;
                border-radius: 3px;
            }
            QSlider::sub-page:horizontal {
                background: #4CAF50;
                border-radius: 3px;
            }
            QSlider::add-page:horizontal {
                background: #333;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #fff;
                border: 1px solid #444;
                width: 14px;
                margin: -4px 0;
                border-radius: 7px;
            }
        """
        self.btn_play.setStyleSheet(style)
        self.btn_stop.setStyleSheet(style)
        self.btn_capture.setStyleSheet(style)
        self.btn_marker.setStyleSheet(style)
        self.btn_fullscreen.setStyleSheet(style)
        self.slider.setStyleSheet(style)

    def handle_player_error(self, error):
        """Handle QMediaPlayer errors with crash protection"""
        try:
            error_messages = {
                QMediaPlayer.NoError: "No error",
                QMediaPlayer.ResourceError: "Resource error - Cannot play the media file.\n\n🔧 Possible solutions:\n• Install Windows Media Feature Pack\n• Install K-Lite Codec Pack\n• Try a different video format (MP4 recommended)\n• Check if file is corrupted",
                QMediaPlayer.FormatError: "Format error - Unsupported video format.\n\n🔧 Possible solutions:\n• Convert to MP4 format\n• Install additional codecs\n• Try a different player",
                QMediaPlayer.NetworkError: "Network error - Connection issue or invalid URL.\n\n🔧 Possible solutions:\n• Check internet connection\n• Verify URL is correct\n• Try again later",
                QMediaPlayer.AccessDeniedError: "Access denied - Permission issue or file is in use.\n\n🔧 Possible solutions:\n• Close other applications using the file\n• Run as administrator\n• Check file permissions",
                QMediaPlayer.ServiceMissingError: "Service missing - Media service is not available.\n\n🔧 Possible solutions:\n• Install Qt multimedia components\n• Install FFmpeg\n• Restart the application",
            }

            self.error_message = error_messages.get(error, f"Unknown media error (code: {error})")
            print(f"🚨 Media Player Error: {self.error_message}")

            # Stop player safely to prevent crashes
            try:
                if self.player and hasattr(self.player, 'stop'):
                    if self.player.state() != QMediaPlayer.StoppedState:
                        self.player.stop()
                    self.player.setMedia(QMediaContent())
            except Exception as stop_error:
                print(f"⚠️ Error stopping player: {stop_error}")

            # Emit error signal safely
            try:
                self.error_occurred.emit(self.error_message)
            except Exception as emit_error:
                print(f"⚠️ Error emitting signal: {emit_error}")

        except Exception as e:
            print(f"❌ Critical error in error handler: {e}")
            # Last resort - try to emit a generic error
            try:
                self.error_occurred.emit("Critical playback error occurred")
            except:
                pass

    def on_error(self, message):
        """Handle error signal"""
        QMessageBox.warning(self, "Playback Error", message)
        
    def on_playback_start(self):
        """Handle playback start signal"""
        print("Playback started successfully")

    def load(self, url_or_path: str):
        """Load video with crash protection and better error handling"""
        if not url_or_path:
            self.error_occurred.emit("No URL or path provided")
            return

        try:
            print(f"🎬 Loading video: {url_or_path}")

            # Reset player state
            self._reset_player_state()

            # Local file
            if os.path.exists(url_or_path):
                print("📁 Loading local file")
                self._load_local_file(url_or_path)
            else:
                # Check URL type and load appropriately
                if any(x in url_or_path for x in ["youtube.com", "youtu.be", "facebook.com", "fb.watch", "instagram.com", "twitter.com", "x.com"]):
                    print("🌐 Loading web content")
                    self._use_webview(url_or_path)
                else:
                    print("🔗 Loading as direct stream")
                    self._load_stream_url(url_or_path)

            # Emit signal after successful load
            QTimer.singleShot(500, self._emit_playback_started)

        except Exception as e:
            print(f"❌ Load error: {e}")
            self.error_occurred.emit(f"Load error: {str(e)}")

    def _reset_player_state(self):
        """Reset player state safely"""
        try:
            if self.player and hasattr(self.player, 'stop'):
                if self.player.state() != QMediaPlayer.StoppedState:
                    self.player.stop()
                self.player.setMedia(QMediaContent())
        except Exception as e:
            print(f"⚠️ Error resetting player: {e}")

    def _emit_playback_started(self):
        """Safely emit playback started signal"""
        try:
            self.playback_started.emit()
        except Exception as e:
            print(f"⚠️ Error emitting signal: {e}")

    def _load_local_file(self, file_path):
        """Load local file with validation and fallback options"""
        abs_path = os.path.abspath(file_path)

        # Validate file
        if not os.access(abs_path, os.R_OK):
            raise PermissionError(f"Cannot read file: {abs_path}")

        file_size = os.path.getsize(abs_path)
        if file_size == 0:
            raise ValueError(f"File is empty: {abs_path}")

        print(f"📊 File size: {file_size} bytes")

        # Try Qt player first, with fallback to alternative methods
        try:
            self._use_qt_player_safe(abs_path)
        except Exception as qt_error:
            print(f"⚠️ Qt player failed: {qt_error}")
            print("🔄 Trying alternative playback methods...")
            self._try_alternative_playback(abs_path)

    def _load_stream_url(self, url):
        """Load stream URL with enhanced YouTube support"""
        try:
            print(f"🔗 Loading stream URL: {url[:100]}...")

            # Validate URL format
            if not url.startswith(('http://', 'https://')):
                raise ValueError(f"Invalid URL format: {url}")

            # Special handling for YouTube URLs
            if 'youtube.com' in url or 'youtu.be' in url:
                print("🎬 Detected YouTube URL - extracting stream")
                stream_url = self._extract_youtube_stream(url)
                if stream_url:
                    url = stream_url
                    print(f"✅ Got YouTube stream: {url[:100]}...")
                else:
                    print("⚠️ Could not extract YouTube stream, using web player")
                    self._use_webview(url)
                    return

            # Load with Qt player
            self._use_qt_player_safe(url)

        except Exception as e:
            print(f"❌ Stream URL load error: {e}")
            self.error_occurred.emit(f"Stream load error: {str(e)}")

    def _extract_youtube_stream(self, url):
        """Extract direct stream URL from YouTube video"""
        try:
            import yt_dlp

            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'format': 'best[height<=720]/best',  # Get best quality up to 720p
                'extractaudio': False,
                'noplaylist': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)

                if info and info.get('url'):
                    return info['url']

                # Try to get from formats
                if info and info.get('formats'):
                    for fmt in info['formats']:
                        if fmt.get('url') and fmt.get('vcodec') != 'none':
                            return fmt['url']

            return None

        except Exception as e:
            print(f"Error extracting YouTube stream: {e}")
            return None

    def _use_qt_player_safe(self, path):
        """Safe wrapper for _use_qt_player with enhanced error handling"""
        try:
            print(f"🎬 _use_qt_player_safe called with: {path}")

            # Pre-validate video file
            if os.path.exists(path):
                validation_result = self._validate_video_file(path)
                if not validation_result['valid']:
                    self.error_occurred.emit(validation_result['error'])
                    return

            self._use_qt_player(path)
        except Exception as e:
            print(f"❌ Error in _use_qt_player_safe: {e}")
            self.error_occurred.emit(f"Playback error: {str(e)}")

    def _use_qt_player(self, path):
        """Use QMediaPlayer for playback with enhanced error handling and codec support"""
        try:
            print(f"_use_qt_player called with path: {path}")

            # Check if player still exists
            if not self.player:
                print("Player object is None - recreating player")
                self._recreate_player()
                if not self.player:
                    raise RuntimeError("Failed to create media player")

            if hasattr(self.webview, 'hide'):
                self.webview.hide()
            if hasattr(self.video_widget, 'show'):
                self.video_widget.show()

            # Stop any current playback safely
            try:
                if self.player.state() != QMediaPlayer.StoppedState:
                    self.player.stop()
                    # Wait a bit for the stop to complete
                    QTimer.singleShot(100, lambda: self._continue_loading(path))
                    return
            except Exception as e:
                print(f"Warning: Error stopping player: {e}")

            self._continue_loading(path)

        except Exception as e:
            print(f"Error in _use_qt_player: {e}")
            import traceback
            traceback.print_exc()
            try:
                self.error_occurred.emit(f"Player error: {str(e)}")
            except:
                pass

    def _continue_loading(self, path):
        """Continue loading after player is stopped"""
        try:
            # Create media content (local file or URL)
            if os.path.exists(path):
                print(f"Loading local file: {path}")
                abs_path = os.path.abspath(path)
                print(f"Absolute path: {abs_path}")

                # Check file permissions
                if not os.access(abs_path, os.R_OK):
                    raise PermissionError(f"Cannot read file: {abs_path}")

                # Check file size
                file_size = os.path.getsize(abs_path)
                print(f"File size: {file_size} bytes")

                if file_size == 0:
                    raise ValueError("File is empty")

                # Check file extension for supported formats
                _, ext = os.path.splitext(abs_path.lower())
                supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
                if ext not in supported_formats:
                    print(f"Warning: {ext} format may not be supported. Supported formats: {supported_formats}")

                # Create media content with enhanced URL handling
                file_url = QUrl.fromLocalFile(abs_path)

                # Ensure URL is properly formatted for Windows
                if not file_url.isValid():
                    # Try alternative URL creation
                    file_url = QUrl(f"file:///{abs_path.replace(os.sep, '/')}")

                media_content = QMediaContent(file_url)
                print(f"Created media content for local file: {media_content.canonicalUrl()}")

                # Validate media content
                if media_content.isNull():
                    raise ValueError("Failed to create valid media content")
            else:
                print(f"Loading URL: {path}")
                # Validate URL format
                if not path.startswith(('http://', 'https://', 'ftp://', 'file://')):
                    raise ValueError(f"Invalid URL format: {path}")
                media_content = QMediaContent(QUrl(path))
                print(f"Created media content for URL: {media_content.canonicalUrl()}")

            # Set media and check if it was successful
            self.player.setMedia(media_content)
            print("Media content set successfully")

            # Check media status after setting
            QTimer.singleShot(100, self._check_media_status)

            # Auto-play after loading
            QTimer.singleShot(1000, self._try_auto_play)

            # Check player state safely
            try:
                print(f"Player state: {self.player.state()}")
                print(f"Player media status: {self.player.mediaStatus()}")
            except:
                print("Could not get player state")

        except Exception as e:
            print(f"Error in _continue_loading: {e}")
            self.error_occurred.emit(f"Loading error: {str(e)}")

    def _try_alternative_playback(self, file_path):
        """Try alternative playback methods when Qt multimedia fails"""
        try:
            print("🔄 Attempting alternative playback...")

            # Method 1: Try opening with system default player
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                # Use Windows Media Player or default video player
                try:
                    subprocess.Popen(['start', '', file_path], shell=True)
                    self.error_occurred.emit(f"Video opened in system player.\n\nFile: {os.path.basename(file_path)}\n\nNote: Qt multimedia components may need to be installed for in-app playback.")
                    return
                except Exception as e:
                    print(f"Failed to open with system player: {e}")

            # Method 2: Show file information and instructions
            file_info = f"""
📁 File Information:
• Path: {file_path}
• Size: {os.path.getsize(file_path)} bytes
• Format: {os.path.splitext(file_path)[1].upper()}

🔧 To enable video playback in this application:
1. Install Windows Media Feature Pack (for Windows N/KN editions)
2. Install K-Lite Codec Pack
3. Restart the application

💡 Alternative: The file can be opened with your system's default video player.
            """

            self.error_occurred.emit(file_info.strip())

        except Exception as e:
            print(f"Alternative playback failed: {e}")
            self.error_occurred.emit(f"Unable to play video file.\n\nPlease install video codecs or try a different file format.")

    def _recreate_player(self):
        """Recreate the media player if it's corrupted"""
        try:
            print("Recreating media player...")

            # Disconnect old signals if player exists
            if self.player:
                try:
                    self.player.error.disconnect()
                    self.player.durationChanged.disconnect()
                    self.player.positionChanged.disconnect()
                    self.player.stateChanged.disconnect()
                except:
                    pass

            # Create new player
            self.player = QMediaPlayer(self)
            self.player.setVideoOutput(self.video_widget)
            self.player.setVolume(80)

            # Reconnect signals
            self.player.error.connect(self.handle_player_error)
            self.player.durationChanged.connect(self._on_dur)
            self.player.positionChanged.connect(self._on_pos)
            self.player.stateChanged.connect(self._on_state_changed)

            print("Media player recreated successfully")

        except Exception as e:
            print(f"Error recreating player: {e}")

    def _validate_video_file(self, file_path):
        """Validate video file before attempting playback"""
        try:
            # Check file exists and is readable
            if not os.path.exists(file_path):
                return {'valid': False, 'error': f"File not found: {file_path}"}

            if not os.access(file_path, os.R_OK):
                return {'valid': False, 'error': f"Cannot read file: {file_path}"}

            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return {'valid': False, 'error': "File is empty"}

            if file_size < 1024:  # Less than 1KB is suspicious
                return {'valid': False, 'error': "File appears to be corrupted (too small)"}

            # Check file extension
            _, ext = os.path.splitext(file_path.lower())
            supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp']

            if ext not in supported_formats:
                return {
                    'valid': False,
                    'error': f"Unsupported format: {ext}\n\nSupported formats: {', '.join(supported_formats)}\n\n🔧 Try converting to MP4 format"
                }

            # Try to probe file with FFmpeg if available
            probe_result = self._probe_video_with_ffmpeg(file_path)
            if probe_result and not probe_result['valid']:
                return probe_result

            return {'valid': True, 'error': None}

        except Exception as e:
            return {'valid': False, 'error': f"Validation error: {str(e)}"}

    def _probe_video_with_ffmpeg(self, file_path):
        """Probe video file with FFmpeg to check if it's valid"""
        try:
            import subprocess

            # Use ffprobe to check video file
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                file_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

            if result.returncode != 0:
                return {
                    'valid': False,
                    'error': f"Video file appears to be corrupted or unsupported.\n\n🔧 Possible solutions:\n• Try re-downloading the file\n• Convert to MP4 format\n• Check if file is complete"
                }

            # Parse JSON output to check for video streams
            import json
            try:
                data = json.loads(result.stdout)
                has_video = any(stream.get('codec_type') == 'video' for stream in data.get('streams', []))

                if not has_video:
                    return {
                        'valid': False,
                        'error': "File contains no video streams.\n\n🔧 This might be an audio-only file or corrupted video."
                    }

            except json.JSONDecodeError:
                pass  # Continue with basic validation

            return {'valid': True, 'error': None}

        except subprocess.TimeoutExpired:
            return {'valid': False, 'error': "File validation timed out - file might be corrupted"}
        except FileNotFoundError:
            # FFmpeg not available, skip probe
            return None
        except Exception as e:
            print(f"FFmpeg probe error: {e}")
            return None

    def _try_auto_play(self):
        """Try to auto-play the loaded media"""
        try:
            if self.player and self.player.mediaStatus() == QMediaPlayer.LoadedMedia:
                print("Auto-playing loaded media...")
                self.play()
            else:
                print(f"Media not ready for auto-play. Status: {self.player.mediaStatus()}")
        except Exception as e:
            print(f"Error in auto-play: {e}")

    def _check_media_status(self):
        """Check media status after loading"""
        try:
            if self.player:
                status = self.player.mediaStatus()
                print(f"Media status: {status}")

                if status == QMediaPlayer.InvalidMedia:
                    self.error_occurred.emit(
                        "Invalid media format detected.\n\n"
                        "🔧 Possible solutions:\n"
                        "• Install Windows Media Feature Pack\n"
                        "• Install K-Lite Codec Pack\n"
                        "• Convert video to MP4 format\n"
                        "• Try a different video file"
                    )
                elif status == QMediaPlayer.UnknownMediaStatus:
                    print("Unknown media status - waiting for load...")
                    # Check again in a moment
                    QTimer.singleShot(500, self._check_media_status)

        except Exception as e:
            print(f"Error checking media status: {e}")

        except Exception as e:
            print(f"Error in _use_qt_player: {e}")
            import traceback
            traceback.print_exc()
            try:
                self.error_occurred.emit(f"Player error: {str(e)}")
            except:
                print("Could not emit error signal")

    def _use_webview(self, url):
        """Use QWebEngineView for web content with error handling"""
        try:
            if not WEB_ENGINE_AVAILABLE:
                raise RuntimeError("Web engine not available - cannot play web content")

            self.player.stop()
            if hasattr(self.video_widget, 'hide'):
                self.video_widget.hide()
            if hasattr(self.webview, 'show'):
                self.webview.show()

            # Validate URL
            if not url.startswith(('http://', 'https://')):
                raise ValueError(f"Invalid web URL: {url}")

            self.webview.setUrl(QUrl(url))
            print(f"WebView loading: {url}")

        except Exception as e:
            print(f"Error in _use_webview: {e}")
            self.error_occurred.emit(f"WebView error: {str(e)}")

    def toggle_play_pause(self):
        """Toggle between play and pause"""
        if self.is_playing:
            self.pause()
        else:
            self.play()

    def toggle_play(self):
        """Toggle between play and pause (alias for compatibility)"""
        self.toggle_play_pause()

    def seek_forward(self):
        """Seek forward by 5 seconds"""
        try:
            current_pos = self.player.position()
            new_pos = min(current_pos + 5000, self.player.duration())  # 5 seconds = 5000ms
            self.player.setPosition(new_pos)
        except Exception as e:
            print(f"Error seeking forward: {e}")

    def seek_backward(self):
        """Seek backward by 5 seconds"""
        try:
            current_pos = self.player.position()
            new_pos = max(current_pos - 5000, 0)  # 5 seconds = 5000ms
            self.player.setPosition(new_pos)
        except Exception as e:
            print(f"Error seeking backward: {e}")
            
    def play(self):
        """Enhanced play method with better error handling"""
        try:
            if not self.player:
                self.error_occurred.emit("Media player not initialized")
                return

            # Check if media is loaded
            media_status = self.player.mediaStatus()
            print(f"Media status before play: {media_status}")

            if media_status == QMediaPlayer.NoMedia:
                self.error_occurred.emit("No media loaded. Please select a video file first.")
                return
            elif media_status == QMediaPlayer.InvalidMedia:
                self.error_occurred.emit("Invalid media format. Please try a different video file.")
                return
            elif media_status == QMediaPlayer.LoadingMedia:
                print("Media still loading, waiting...")
                # Wait a bit and try again
                QTimer.singleShot(1000, self.play)
                return

            # Try to play
            self.player.play()
            self.is_playing = True

            # Update play button icon if it exists
            if hasattr(self, 'btn_play'):
                try:
                    self.btn_play.setText("⏸️")  # Pause icon
                except:
                    pass

            print("Play command sent successfully")

        except Exception as e:
            print(f"Error in play method: {e}")
            self.error_occurred.emit(f"Play error: {str(e)}")

    def pause(self):
        """Enhanced pause method"""
        try:
            if self.player:
                self.player.pause()
                self.is_playing = False

                # Update play button icon if it exists
                if hasattr(self, 'btn_play'):
                    try:
                        self.btn_play.setText("▶️")  # Play icon
                    except:
                        pass

                print("Video paused")
        except Exception as e:
            print(f"Error pausing video: {e}")

    def stop(self):
        """Enhanced stop method"""
        try:
            if self.player:
                self.player.stop()
                self.is_playing = False

                # Update play button icon if it exists
                if hasattr(self, 'btn_play'):
                    try:
                        self.btn_play.setText("▶️")  # Play icon
                    except:
                        pass

                print("Video stopped")
        except Exception as e:
            print(f"Error stopping video: {e}")

    def _on_pos(self, ms):
        dur = max(1, self.player.duration())
        self.slider.blockSignals(True)
        self.slider.setValue(int(1000 * ms / dur))
        self.slider.blockSignals(False)
        
        # Update time display
        self.update_time_display(ms, dur)
        self.time_changed.emit(ms)

    def _on_dur(self, ms):
        if ms > 0:
            self.duration_loaded.emit(ms)
            self.update_time_display(self.player.position(), ms)

    def update_time_display(self, current, total):
        """Update time labels with formatted time"""
        current_secs = current // 1000
        total_secs = total // 1000
        
        current_str = f"{current_secs//3600:02d}:{(current_secs%3600)//60:02d}:{current_secs%60:02d}"
        total_str = f"{total_secs//3600:02d}:{(total_secs%3600)//60:02d}:{total_secs%60:02d}"
        
        self.current_time_label.setText(current_str)
        self.total_time_label.setText(total_str)

    def _on_seek(self, val):
        dur = self.player.duration()
        if dur:
            self.player.setPosition(int(dur * (val / 1000.0)))

    def _on_volume_changed(self, volume):
        """Handle volume slider changes"""
        self.player.setVolume(volume)
        self.volume_label.setText(f"{volume}%")

        # Update volume icon based on level
        if volume == 0:
            self.volume_icon.setText("🔇")
        elif volume < 30:
            self.volume_icon.setText("🔈")
        elif volume < 70:
            self.volume_icon.setText("🔉")
        else:
            self.volume_icon.setText("🔊")

    def _on_state_changed(self, state):
        """Handle player state changes"""
        if state == QMediaPlayer.PlayingState:
            self.is_playing = True
            self.btn_play.setIcon(QIcon.fromTheme("media-playback-pause"))
            self.btn_play.setToolTip("Pause")
        else:
            self.is_playing = False
            self.btn_play.setIcon(QIcon.fromTheme("media-playback-start"))
            self.btn_play.setToolTip("Play")

    def toggle_fullscreen(self):
        if self.video_widget.isVisible():
            if self.video_widget.isFullScreen():
                self.video_widget.setFullScreen(False)
            else:
                self.video_widget.setFullScreen(True)

    def capture_current_frame(self):
        """Capture the current video frame for editing"""
        # This is a simplified implementation
        # In a real application, you would capture the actual video frame
        try:
            # Create a mock frame (in reality, you'd capture from the video)
            frame = QImage(320, 240, QImage.Format_RGB32)
            frame.fill(Qt.darkGray)
            
            painter = QPainter(frame)
            painter.setPen(Qt.white)
            painter.setFont(QFont("Arial", 20))
            painter.drawText(frame.rect(), Qt.AlignCenter, "Captured Frame")
            painter.end()
            
            self.current_frame = frame
            self.frame_captured.emit(frame)
            
        except Exception as e:
            self.error_occurred.emit(f"Frame capture error: {str(e)}")

    def add_marker(self):
        """Add a marker at the current position"""
        position = self.player.position()
        duration = self.player.duration()
        
        if duration > 0:
            marker_pos = position / duration
            self.markers.append(marker_pos)
            self.update_markers_display()
            
    def update_markers_display(self):
        """Update the markers visualization"""
        # This would be implemented to visually show markers on the timeline
        pass
        
    def seek_to_marker(self, marker_index):
        """Seek to a specific marker"""
        if 0 <= marker_index < len(self.markers):
            position = int(self.markers[marker_index] * self.player.duration())
            self.player.setPosition(position)

    def open_video_editor(self):
        """Open video editor for the current video"""
        try:
            # Get current video info
            current_media = self.player.media()
            if current_media.isNull():
                QMessageBox.warning(self, "No Video", "Please load a video first before editing.")
                return

            # Get video URL/path
            video_url = current_media.canonicalUrl().toString()
            if not video_url:
                QMessageBox.warning(self, "No Video", "No video is currently loaded.")
                return

            # Get current position for start time
            current_pos = self.player.position() / 1000.0  # Convert to seconds
            duration = self.player.duration() / 1000.0 if self.player.duration() > 0 else 60.0

            # For now, use current position as start and +30 seconds as end
            start_time = max(0, current_pos)
            end_time = min(duration, current_pos + 30)

            print(f"🎬 Opening video editor for: {video_url}")
            print(f"⏱️ Segment: {start_time:.1f}s - {end_time:.1f}s")

            # Import and create video editor
            from .video_editor import VideoEditorWindow

            # Create editor window
            self.video_editor = VideoEditorWindow(
                video_path=video_url,
                start_time=start_time,
                end_time=end_time,
                parent=self
            )

            # Show editor
            self.video_editor.show()

            # Show info message
            QMessageBox.information(self, "Video Editor",
                                  f"Video Editor opened!\n\n"
                                  f"Editing segment: {start_time:.1f}s - {end_time:.1}s\n"
                                  f"Duration: {end_time - start_time:.1f}s\n\n"
                                  f"Use the timeline to adjust the segment and apply effects.")

        except Exception as e:
            print(f"❌ Error opening video editor: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open video editor:\n{str(e)}")
